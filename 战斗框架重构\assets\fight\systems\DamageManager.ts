import { ICharacter } from "../types/ICharacter";
import { DamageType, DamageTag, IDamageInfo } from "../types/IDamage";
import { EventManager } from "./EventManager";
import FightEvent from "../types/FightEvent";

/**
 * 简化的伤害信息实现
 */
class DamageInfo implements IDamageInfo {
    readonly id: string;
    readonly attacker: ICharacter | null;
    readonly target: ICharacter;
    baseDamage: number;
    private _finalDamage: number;
    readonly damageType: DamageType;
    readonly tags: DamageTag[];
    isCritical: boolean = false;
    criticalMultiplier: number = 1.0;
    damageReduction: number = 0;
    damageAmplification: number = 0;
    attachedBuffs: any[] = [];
    source?: string;
    isDodged: boolean = false;

    constructor(
        attacker: ICharacter | null,
        target: ICharacter,
        baseDamage: number,
        damageType: DamageType,
        tags: DamageTag[] = []
    ) {
        this.id = `damage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        this.attacker = attacker;
        this.target = target;
        this.baseDamage = baseDamage;
        this._finalDamage = baseDamage;
        this.damageType = damageType;
        this.tags = [...tags];
    }

    get finalDamage(): number {
        return this._finalDamage;
    }

    set finalDamage(value: number) {
        this._finalDamage = value;
    }

    calculateFinalDamage(): number {
        return this._finalDamage;
    }

    addTag(tag: DamageTag): void {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }

    hasTag(tag: DamageTag): boolean {
        return this.tags.includes(tag);
    }

    addAttachedBuff(buff: any): void {
        this.attachedBuffs.push(buff);
    }
}

/**
 * 伤害管理器
 * 负责处理复杂的伤害计算，包括暴击、抗性、伤害修正等
 */
export class DamageManager {
    private static _instance: DamageManager;
    private _eventManager: EventManager;
    private _totalDamageDealt: number = 0;
    private _totalDamageTaken: number = 0;
    private _criticalHits: number = 0;
    private _totalHits: number = 0;

    constructor() {
        this._eventManager = new EventManager();
    }

    /** 获取单例实例 */
    static getInstance(): DamageManager {
        if (!DamageManager._instance) {
            DamageManager._instance = new DamageManager();
        }
        return DamageManager._instance;
    }

    /**
     * 处理伤害
     * @param attacker 攻击者
     * @param target 目标
     * @param baseDamage 基础伤害
     * @param damageType 伤害类型
     * @param tags 伤害标签
     * @returns 最终伤害信息
     */
    dealDamage(
        attacker: ICharacter,
        target: ICharacter,
        baseDamage: number,
        damageType: DamageType = DamageType.PHYSICAL,
        tags: DamageTag[] = []
    ): IDamageInfo {
        // 创建伤害信息
        const damageInfo = new DamageInfo(attacker, target, baseDamage, damageType, tags);

        // 检查闪避
        if (this.checkDodge(attacker, target)) {
            damageInfo.isDodged = true;
            damageInfo.finalDamage = 0;
            console.log(`${target.name} dodged attack from ${attacker.name}`);
            this._eventManager.emit(FightEvent.takeDamage, damageInfo);
            return damageInfo;
        }

        // 计算暴击
        if (this.checkCritical(attacker, target)) {
            damageInfo.isCritical = true;
            const criticalDamage = (attacker.attributes as any).criticalDamage || 1.5;
            damageInfo.finalDamage *= criticalDamage;
            this._criticalHits++;
            console.log(`Critical hit! ${attacker.name} deals critical damage to ${target.name}`);
        }

        // 应用防御力
        damageInfo.finalDamage = this.applyDefense(damageInfo.finalDamage, target, damageType);

        // 应用伤害减免
        damageInfo.finalDamage = this.applyDamageReduction(damageInfo, target);

        // 应用攻击者的伤害修正（来自Buff等）
        damageInfo.finalDamage = this.applyAttackerModifiers(damageInfo, attacker);

        // 应用目标的伤害修正（来自Buff等）
        damageInfo.finalDamage = this.applyTargetModifiers(damageInfo, target);

        // 确保伤害不为负数
        damageInfo.finalDamage = Math.max(0, Math.floor(damageInfo.finalDamage));

        // 计算实际伤害减免
        damageInfo.damageReduction = damageInfo.baseDamage - damageInfo.finalDamage;

        // 更新统计
        this._totalDamageDealt += damageInfo.finalDamage;
        this._totalHits++;

        // 应用伤害到目标
        if (damageInfo.finalDamage > 0) {
            target.takeDamage(damageInfo.finalDamage, attacker);
            this._totalDamageTaken += damageInfo.finalDamage;
        }

        console.log(`${attacker.name} deals ${damageInfo.finalDamage} ${damageType} damage to ${target.name}`);

        // 触发伤害事件
        this._eventManager.emit(FightEvent.takeDamage, damageInfo);

        return damageInfo;
    }

    /** 检查是否闪避 */
    private checkDodge(attacker: ICharacter, target: ICharacter): boolean {
        const hitRate = (attacker.attributes as any).hitRate || 1.0;
        const dodgeRate = (target.attributes as any).dodgeRate || 0.0;
        const finalHitRate = Math.max(0, hitRate - dodgeRate);
        return Math.random() > finalHitRate;
    }

    /** 检查是否暴击 */
    private checkCritical(attacker: ICharacter, _target: ICharacter): boolean {
        const criticalRate = (attacker.attributes as any).criticalRate || 0.0;
        // 这里可以添加目标的暴击抗性
        return Math.random() < criticalRate;
    }

    /** 应用防御力 */
    private applyDefense(damage: number, target: ICharacter, damageType: DamageType): number {
        let defense = 0;

        switch (damageType) {
            case DamageType.PHYSICAL:
                defense = target.attributes.defense || 0;
                break;
            case DamageType.MAGIC:
                // 魔法防御力（如果有的话）
                defense = (target.attributes as any).magicDefense || target.attributes.defense * 0.5;
                break;
            case DamageType.TRUE:
                // 真实伤害无视防御
                return damage;
        }

        // 简单的防御计算：伤害 = 基础伤害 - 防御力
        // 可以改为更复杂的公式，如：伤害 = 基础伤害 * (100 / (100 + 防御力))
        return Math.max(1, damage - defense);
    }

    /** 应用伤害减免 */
    private applyDamageReduction(damageInfo: IDamageInfo, _target: ICharacter): number {
        let damage = damageInfo.finalDamage;

        // 这里可以添加基于伤害类型的减免
        // 例如：火焰抗性、冰霜抗性等

        return damage;
    }

    /** 应用攻击者的伤害修正 */
    private applyAttackerModifiers(damageInfo: IDamageInfo, attacker: ICharacter): number {
        let damage = damageInfo.finalDamage;

        // 遍历攻击者的所有Buff，应用伤害修正
        for (const buff of attacker.buffs) {
            if (buff.onDealDamage) {
                const modifiedInfo = buff.onDealDamage(damageInfo, damageInfo.target);
                damage = modifiedInfo.finalDamage;
            }
        }

        return damage;
    }

    /** 应用目标的伤害修正 */
    private applyTargetModifiers(damageInfo: IDamageInfo, target: ICharacter): number {
        let damage = damageInfo.finalDamage;

        // 遍历目标的所有Buff，应用伤害修正
        for (const buff of target.buffs) {
            if (buff.onTakeDamage) {
                const modifiedInfo = buff.onTakeDamage(damageInfo, damageInfo.attacker);
                damage = modifiedInfo.finalDamage;
            }
        }

        return damage;
    }

    /** 获取统计信息 */
    getStats(): {
        totalDamageDealt: number;
        totalDamageTaken: number;
        totalHits: number;
        criticalHits: number;
        criticalRate: number;
    } {
        return {
            totalDamageDealt: this._totalDamageDealt,
            totalDamageTaken: this._totalDamageTaken,
            totalHits: this._totalHits,
            criticalHits: this._criticalHits,
            criticalRate: this._totalHits > 0 ? this._criticalHits / this._totalHits : 0
        };
    }

    /** 重置统计信息 */
    resetStats(): void {
        this._totalDamageDealt = 0;
        this._totalDamageTaken = 0;
        this._criticalHits = 0;
        this._totalHits = 0;
    }

    /** 设置事件监听器 */
    on(event: string, callback: Function): void {
        this._eventManager.on(event, callback);
    }

    /** 移除事件监听器 */
    off(event: string, callback: Function): void {
        this._eventManager.off(event, callback);
    }

    /** 清理管理器 */
    cleanup(): void {
        this.resetStats();
        this._eventManager.cleanup();
    }
}
