import { BuffManager } from "../systems/BuffManager";
import { EventManager } from "../systems/EventManager";
import { SkillManager } from "../systems/SkillManager";
import { CharacterRole, CharacterState, CharacterSelectTag } from "../types/CharacterTypes";
import FightEvent from "../types/FightEvent";
import { IBuff } from "../types/IBuff";
import { ICharacter, ICharacterEvents } from "../types/ICharacter";
import { ICharacterAttributes } from "../types/ICharacterAttributes";
import { ISkill } from "../types/ISkill";
import { CharacterAttributes } from "./CharacterAttributes";

/*** 角色基类*/
export abstract class BaseCharacter extends cc.Component implements ICharacter {
    // 基本属性
    protected _id: string;
    protected _name: string;
    protected _role: CharacterRole;
    protected _state: CharacterState = CharacterState.IDLE;
    protected _selectTag: CharacterSelectTag = CharacterSelectTag.NONE;

    // 组件管理器
    protected _attributes: ICharacterAttributes;
    protected _skillManager: SkillManager;
    protected _buffManager: BuffManager;
    protected _eventManager: EventManager;
    // 动画和音效
    protected _spine: sp.Skeleton;
    protected _animationConfig: any;
    protected _soundConfig: any;
    // 节点引用
    protected _fireNode: cc.Node;
    protected _fireBaseNode: cc.Node;
    // 事件回调
    protected _events: ICharacterEvents = {};

    // 实现ICharacter接口
    get id(): string { return this._id; }
    get characterName(): string { return this._name; }
    get role(): CharacterRole { return this._role; }
    get attributes(): ICharacterAttributes { return this._attributes; }
    get isDead(): boolean { return this._state === CharacterState.DEAD; }
    get skills(): ReadonlyArray<ISkill> { return this._skillManager.getAllSkills(); }
    get buffs(): ReadonlyArray<IBuff> { return this._buffManager.buffs; }
    // 额外属性
    get state(): CharacterState { return this._state; }
    get selectTag(): CharacterSelectTag { return this._selectTag; }
    get spine(): sp.Skeleton { return this._spine; }
    set selectTag(value: CharacterSelectTag) { this._selectTag = value; }

    /*** 初始化角色*/
    protected onLoad(): void {
        this.initializeId();
        this.initializeComponents();
        this.initializeNodes();
        this.setupEventListeners();
    }

    /** * 启动角色 */
    protected start(): void {
        this.onCharacterStart();
    }

    /** * 更新角色状态 */
    update(deltaTime: number): void {
        if (this.isDead) return;
        this._skillManager.update(deltaTime);
        this._buffManager.update(deltaTime);
        this.onCharacterUpdate(deltaTime);
    }

    /** * 销毁角色 */
    protected onDestroy(): void {
        this.cleanup();
    }

    /** * 初始化ID */
    protected initializeId(): void {
        this._id = this.generateUniqueId();
    }

    /** * 初始化组件 */
    protected initializeComponents(): void {
        this._attributes = new CharacterAttributes();
        this._skillManager = new SkillManager(this);
        this._buffManager = new BuffManager(this);
        this._eventManager = new EventManager();
    }

    /** * 初始化节点引用 */
    protected initializeNodes(): void {
        this._spine = this.getComponent(sp.Skeleton);
        this._fireNode = this.node.getChildByName("fireNode");
        this._fireBaseNode = this.node.getChildByName("fireBaseNode");
    }

    /** * 设置事件监听器 */
    protected setupEventListeners(): void {
        // 监听属性变化
        this._eventManager.on(FightEvent.attributeChanged, this.onAttributeChanged.bind(this));
        // 监听状态变化
        this._eventManager.on(FightEvent.stateChanged, this.onStateChanged.bind(this));
        // 监听死亡事件
        this._eventManager.on(FightEvent.death, this.onDeath.bind(this));
    }

    /** * 移动角色 */
    move(direction: cc.Vec3): boolean {
        if (this.isDead || this._state === CharacterState.STUNNED) {
            return false;
        }
        if (this.canMove()) {
            this.setState(CharacterState.MOVING);
            this.performMove(direction);
            return true;
        }
        return false;
    }

    /** * 攻击目标 */
    attack(target?: ICharacter): boolean {
        if (this.isDead || this._state === CharacterState.STUNNED) {
            return false;
        }
        const attackSkill = this._skillManager.getBasicAttackSkill();
        if (attackSkill) {
            return this.castSkill(attackSkill.id, target?.node);
        }
        return false;
    }

    /** * 释放技能 */
    castSkill(skillName: string, target?: cc.Node): boolean {
        if (this.isDead || this._state === CharacterState.SILENCED) {
            return false;
        }
        const skill = this._skillManager.getSkill(skillName);
        if (!skill || !skill.canUse) {
            return false;
        }
        if (!this.checkResourceCost(skill)) {
            return false;
        }
        this.consumeResources(skill);
        this.setState(CharacterState.CASTING);
        const success = skill.cast(this, this.getCharacterFromNode(target), undefined, this.getWorldPosition(target));
        if (success) {
            this._events.onSkillCast?.(this, skillName);
            this._eventManager.emit(FightEvent.skillCast, { character: this, skillName });
        }
        return success;
    }
    /** * 学习技能 */
    learnSkill(skill: ISkill): void {
        this._skillManager.addSkill(skill);
    }
    /** * 添加Buff */
    addBuff(buff: IBuff): void {
        this._buffManager.addBuff(buff);
        this._events.onBuffAdded?.(this, buff);
        this._eventManager.emit(FightEvent.buffAdded, { character: this, buff });
    }
    /*** 移除Buff*/
    removeBuff(buffId: string): void {
        const removed = this._buffManager.removeBuff(buffId);
        if (removed) {
            this._events.onBuffRemoved?.(this, buffId);
            this._eventManager.emit(FightEvent.buffRemoved, { character: this, buffId });
        }
    }
    /*** 受到伤害*/
    takeDamage(damage: number, attacker?: ICharacter): void {
        if (this.isDead || this._state === CharacterState.INVINCIBLE) {
            return;
        }
        this._attributes.modifyHp(-damage);
        this._events.onTakeDamage?.(this, damage, attacker);
        this._eventManager.emit(FightEvent.takeDamage, { character: this, damage, attacker });
        if (this._attributes.isDead()) {
            this.die();
        }
    }
    /** * 治疗 */
    heal(healAmount: number): void {
        if (this.isDead) return;
        this._attributes.modifyHp(healAmount);
        this._events.onHeal?.(this, healAmount);
        this._eventManager.emit(FightEvent.heal, { character: this, healAmount });
    }
    /** * 死亡处理 */
    die(): void {
        if (this.isDead) return;
        this.setState(CharacterState.DEAD);
        this._events.onDeath?.(this);
        this._eventManager.emit(FightEvent.death, { character: this });
        this.onCharacterDeath();
    }
    /** * 设置事件监听器 */
    setEvents(events: ICharacterEvents): void {
        this._events = { ...this._events, ...events };
    }
    /**  * 设置状态  */
    protected setState(newState: CharacterState): void {
        if (this._state !== newState) {
            const oldState = this._state;
            this._state = newState;
            this._eventManager.emit(FightEvent.stateChanged, { character: this, oldState, newState });
        }
    }
    /**   * 检查是否可以移动   */
    protected canMove(): boolean {
        return this._state === CharacterState.IDLE || this._state === CharacterState.MOVING;
    }
    /** * 检查资源消耗 */
    protected checkResourceCost(_skill: ISkill): boolean {
        // 这里应该检查MP、耐力等资源
        return true; // 简化实现
    }
    /*** 消耗资源    */
    protected consumeResources(_skill: ISkill): void {
        // 这里应该消耗MP、耐力等资源
    }
    /** * 从节点获取角色 */
    protected getCharacterFromNode(node?: cc.Node): ICharacter | undefined {
        return node?.getComponent(BaseCharacter) as ICharacter;
    }
    /** * 获取世界坐标 */
    protected getWorldPosition(node?: cc.Node): cc.Vec3 | undefined {
        return node?.convertToWorldSpaceAR(cc.Vec3.ZERO);
    }
    /** * 生成唯一ID */
    protected generateUniqueId(): string {
        return `char_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    /*** 清理资源*/
    protected cleanup(): void {
        this._skillManager?.cleanup();
        this._buffManager?.cleanup();
        this._eventManager?.cleanup();
    }
    // 抽象方法，子类需要实现
    protected abstract onCharacterStart(): void;
    protected abstract onCharacterUpdate(deltaTime: number): void;
    protected abstract onCharacterDeath(): void;
    protected abstract performMove(direction: cc.Vec3): void;
    /**属性变化处理 */
    protected onAttributeChanged(_event: any): void {
    }
    protected onStateChanged(_event: any): void {
        this.updateAnimation();
    }
    protected onDeath(_event: any): void {
        this.playDeathAnimation();
    }
    /*** 更新动画*/
    protected updateAnimation(): void {
        if (!this._spine || !this._animationConfig) return;
        let animationName = "";
        switch (this._state) {
            case CharacterState.IDLE:
                animationName = this._animationConfig.idle;
                break;
            case CharacterState.MOVING:
                animationName = this._animationConfig.move;
                break;
            case CharacterState.ATTACKING:
                animationName = this._animationConfig.attack;
                break;
            case CharacterState.DEAD:
                animationName = this._animationConfig.death;
                break;
        }
        if (animationName) {
            this._spine.setAnimation(0, animationName, this._state !== CharacterState.DEAD);
        }
    }
    /** * 播放死亡动画 */
    protected playDeathAnimation(): void {
        if (this._spine && this._animationConfig?.death) {
            this._spine.setAnimation(0, this._animationConfig.death, false);
        }
    }
}
