import { BuffModelBeHurtFight } from "../actions/BuffModelBeHurtFight";
import { Character } from "../characters/Character";
import { PlayerSkillFire1 } from "../skills/PlayerSkillFire1";
import { BattleManager } from "../systems/BattleManager";
import { CharacterCreateInfo, CharacterRole } from "../types/CharacterTypes";
import SkillName from "../types/SkillName";


const { ccclass, property } = cc._decorator;
/*** 战斗测试场景*/
@ccclass
export class BattleTestScene extends cc.Component {
    @property(cc.Node)
    public uiRoot: cc.Node = null;
    @property(cc.Label)
    public statusLabel: cc.Label = null;
    @property(cc.Button)
    public startBattleBtn: cc.Button = null;
    @property(cc.Button)
    public castSkillBtn: cc.Button = null;
    @property(cc.Button)
    public addBuffBtn: cc.Button = null;

    private player: Character = null;
    private enemy: Character = null;
    private battleManager: BattleManager = null;
    private battleStarted: boolean = false;

    onLoad() {
        /*** 设置UI*/
        this.startBattleBtn?.node.on('click', this.onStartBattle, this);
        this.castSkillBtn?.node.on('click', this.onCastSkill, this);
        this.addBuffBtn?.node.on('click', this.onAddBuff, this);

        // 初始化战斗管理器
        this.battleManager = BattleManager.getInstance();
        console.log("战斗管理器已初始化");
    }
    start() {
        this.createPlayer();
        this.createEnemy();
        console.log("角色创建完成");
        this.updateStatus();
    }
    onDestroy() {
        if (this.battleManager) {
            this.battleManager.cleanup();
        }
    }
    update(dt: number) {
        if (this.battleStarted && this.battleManager) {
            // 使用BattleManager统一更新
            this.battleManager.update(dt);
        }
        this.updateStatus();
    }
    /*** 创建玩家*/
    private createPlayer(): void {
        const playerNode = new cc.Node("Player");
        playerNode.parent = this.node;
        playerNode.position = cc.v3(-200, 0, 0);
        this.player = playerNode.addComponent(Character);
        const playerData: CharacterCreateInfo = {
            prefabKey: "player",
            role: CharacterRole.HERO,
            name: "测试玩家",
            worldPosition: cc.v3(-200, 0, 0),
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                attack: 100,
                defense: 50,
                attackSpeed: 1.2,
                moveSpeed: 150,
                attackRange: 300,
                criticalRate: 0.1,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.05,
                level: 10,
                experience: 0
            }
        };
        this.player.setCharacterData(playerData);
        // 学习技能
        const fireSkill = new PlayerSkillFire1();
        this.player.learnSkill(fireSkill);
        console.log("玩家创建完成:", this.player.getCharacterInfo());
    }
    /** * 创建敌人 */
    private createEnemy(): void {
        const enemyNode = new cc.Node("Enemy");
        enemyNode.parent = this.node;
        enemyNode.position = cc.v3(200, 0, 0);
        this.enemy = enemyNode.addComponent(Character);
        const enemyData: CharacterCreateInfo = {
            prefabKey: "enemy",
            role: CharacterRole.ENEMY,
            name: "测试敌人",
            worldPosition: cc.v3(200, 0, 0),
            initialAttributes: {
                hp: 500,
                maxHp: 500,
                mp: 100,
                maxMp: 100,
                attack: 80,
                defense: 30,
                attackSpeed: 1.0,
                moveSpeed: 100,
                attackRange: 150,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.9,
                dodgeRate: 0.1,
                level: 5,
                experience: 0
            }
        };
        this.enemy.setCharacterData(enemyData);
        console.log("敌人创建完成:", this.enemy.getCharacterInfo());
    }
    /** * 开始战斗 */
    private onStartBattle(): void {
        if (this.battleStarted) {
            console.log("战斗已经开始");
            return;
        }

        if (!this.player || !this.enemy) {
            console.log("角色未创建完成，无法开始战斗");
            return;
        }

        this.battleStarted = true;

        // 使用BattleManager开始战斗
        const participants = [this.player, this.enemy];
        this.battleManager.startBattle("test_battle_" + Date.now(), participants);

        console.log("=== 战斗开始 ===");
        if (this.castSkillBtn) {
            this.castSkillBtn.interactable = true;
        }
        if (this.addBuffBtn) {
            this.addBuffBtn.interactable = true;
        }
        if (this.startBattleBtn) {
            this.startBattleBtn.interactable = false;
        }
    }

    /**  * 释放技能  */
    private onCastSkill(): void {
        if (!this.battleStarted || !this.player || !this.enemy) {
            console.log("战斗未开始或角色不存在");
            return;
        }
        if (this.player.isDead || this.enemy.isDead) {
            console.log("有角色已死亡，无法释放技能");
            return;
        }
        const success = this.player.castSkill(SkillName.player_skill_fire1, this.enemy.node);
        if (success) {
            console.log(`${this.player.name} 对 ${this.enemy.name} 释放了火球术！`);
        } else {
            console.log("技能释放失败");
        }
    }

    /** * 添加Buff */
    private onAddBuff(): void {
        if (!this.battleStarted || !this.player) {
            console.log("战斗未开始或玩家不存在");
            return;
        }
        if (this.player.isDead) {
            console.log("玩家已死亡，无法添加Buff");
            return;
        }
        const buff = new BuffModelBeHurtFight(this.player, this.player);
        this.player.addBuff(buff);
        console.log(`${this.player.name} 获得了反击Buff！`);
    }

    /** * 更新状态显示 */
    private updateStatus(): void {
        if (!this.statusLabel) return;
        let status = "=== 战斗系统测试 ===\n";
        if (this.player) {
            const playerInfo = this.player.getCharacterInfo();
            status += `玩家: ${playerInfo.name}\n`;
            status += `HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}\n`;
            status += `MP: ${(this.player.attributes as any).currentMp || 0}/${(this.player.attributes as any).maxMp || 0}\n`;
            status += `状态: ${playerInfo.isDead ? "死亡" : "存活"}\n`;
            status += `技能数: ${playerInfo.skillCount}\n`;
            status += `Buff数: ${playerInfo.buffCount}\n\n`;
        }
        if (this.enemy) {
            const enemyInfo = this.enemy.getCharacterInfo();
            status += `敌人: ${enemyInfo.name}\n`;
            status += `HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}\n`;
            status += `状态: ${enemyInfo.isDead ? "死亡" : "存活"}\n\n`;
        }
        if (this.battleManager) {
            const battleStats = this.battleManager.getBattleStats();
            status += `战斗状态:\n`;
            status += `进行中: ${battleStats.isInBattle ? "是" : "否"}\n`;
            status += `时长: ${battleStats.duration.toFixed(1)}s\n`;
            status += `参战者: ${battleStats.participantCount}\n\n`;

            status += `Timeline统计:\n`;
            status += `活跃: ${battleStats.timelineStats.activeCount}\n`;
            status += `暂停: ${battleStats.timelineStats.pausedCount}\n`;
            status += `总执行: ${battleStats.timelineStats.totalExecutedEvents}\n`;
        }
        this.statusLabel.string = status;
    }
}
