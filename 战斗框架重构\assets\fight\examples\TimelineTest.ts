import { BattleManager } from "../systems/BattleManager";
import { TimelineManager } from "../systems/TimelineManager";
import { Timeline, TimelineNode } from "../timeline/Timeline";
import { DamageTimelineEvent, PlaySoundTimelineEvent } from "../timeline/TimelineEvents";
import { Character } from "../characters/Character";
import { CharacterCreateInfo, CharacterRole } from "../types/CharacterTypes";
import { DamageType } from "../types/IDamage";

const { ccclass, property } = cc._decorator;

/**
 * Timeline系统测试
 */
@ccclass
export class TimelineTest extends cc.Component {
    @property(cc.Button)
    public testTimelineBtn: cc.Button = null;
    
    @property(cc.Button)
    public testBattleBtn: cc.Button = null;
    
    @property(cc.Label)
    public statusLabel: cc.Label = null;

    private battleManager: BattleManager = null;
    private timelineManager: TimelineManager = null;
    private player: Character = null;
    private enemy: Character = null;

    onLoad() {
        // 初始化管理器
        this.battleManager = BattleManager.getInstance();
        this.timelineManager = this.battleManager.timelineManager;
        
        // 设置按钮事件
        if (this.testTimelineBtn) {
            this.testTimelineBtn.node.on('click', this.testTimeline, this);
        }
        if (this.testBattleBtn) {
            this.testBattleBtn.node.on('click', this.testBattle, this);
        }
        
        console.log("TimelineTest initialized");
    }

    start() {
        this.createTestCharacters();
        this.updateStatus();
    }

    update(dt: number) {
        if (this.battleManager && this.battleManager.isInBattle) {
            this.battleManager.update(dt);
        }
        this.updateStatus();
    }

    /** 创建测试角色 */
    private createTestCharacters(): void {
        // 创建玩家
        const playerNode = new cc.Node("TestPlayer");
        playerNode.parent = this.node;
        playerNode.position = cc.v3(-100, 0, 0);
        
        this.player = playerNode.addComponent(Character);
        const playerData: CharacterCreateInfo = {
            prefabKey: "player",
            role: CharacterRole.HERO,
            name: "测试玩家",
            worldPosition: cc.v3(-100, 0, 0),
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                attack: 100,
                defense: 50,
                attackSpeed: 1.0,
                moveSpeed: 100,
                attackRange: 200,
                criticalRate: 0.1,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.05,
                level: 10,
                experience: 0
            }
        };
        this.player.setCharacterData(playerData);

        // 创建敌人
        const enemyNode = new cc.Node("TestEnemy");
        enemyNode.parent = this.node;
        enemyNode.position = cc.v3(100, 0, 0);
        
        this.enemy = enemyNode.addComponent(Character);
        const enemyData: CharacterCreateInfo = {
            prefabKey: "enemy",
            role: CharacterRole.ENEMY,
            name: "测试敌人",
            worldPosition: cc.v3(100, 0, 0),
            initialAttributes: {
                hp: 500,
                maxHp: 500,
                mp: 100,
                maxMp: 100,
                attack: 80,
                defense: 30,
                attackSpeed: 1.0,
                moveSpeed: 100,
                attackRange: 150,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.9,
                dodgeRate: 0.1,
                level: 5,
                experience: 0
            }
        };
        this.enemy.setCharacterData(enemyData);
        
        console.log("Test characters created");
    }

    /** 测试Timeline功能 */
    private testTimeline(): void {
        if (!this.player || !this.enemy) {
            console.log("Characters not ready");
            return;
        }

        console.log("=== Testing Timeline ===");
        
        // 创建一个简单的Timeline
        const timeline = new Timeline(
            "test_timeline_" + Date.now(),
            "测试Timeline",
            3.0,
            this.player,
            this.enemy
        );

        // 添加伤害事件
        const damageEvent = new DamageTimelineEvent("damage_1", 50, DamageType.PHYSICAL);
        const damageNode = new TimelineNode("damage_node", 1.0, damageEvent);
        timeline.addNode(damageNode);

        // 添加音效事件
        const soundEvent = new PlaySoundTimelineEvent("sound_1", "test_sound");
        const soundNode = new TimelineNode("sound_node", 0.5, soundEvent);
        timeline.addNode(soundNode);

        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        
        console.log("Timeline created and added to manager");
        console.log("Timeline stats:", this.timelineManager.getStats());
    }

    /** 测试战斗管理器 */
    private testBattle(): void {
        if (!this.player || !this.enemy) {
            console.log("Characters not ready");
            return;
        }

        console.log("=== Testing BattleManager ===");
        
        if (this.battleManager.isInBattle) {
            console.log("Ending current battle");
            this.battleManager.endBattle("manual_stop");
        } else {
            console.log("Starting new battle");
            const participants = [this.player, this.enemy];
            this.battleManager.startBattle("timeline_test_battle", participants);
        }
    }

    /** 更新状态显示 */
    private updateStatus(): void {
        if (!this.statusLabel) return;

        let status = "=== Timeline系统测试 ===\n";
        
        if (this.player) {
            status += `玩家: ${this.player.name}\n`;
            status += `HP: ${(this.player.attributes as any).currentHp}/${(this.player.attributes as any).maxHp}\n`;
        }
        
        if (this.enemy) {
            status += `敌人: ${this.enemy.name}\n`;
            status += `HP: ${(this.enemy.attributes as any).currentHp}/${(this.enemy.attributes as any).maxHp}\n`;
        }
        
        if (this.battleManager) {
            const battleStats = this.battleManager.getBattleStats();
            status += `\n战斗状态: ${battleStats.isInBattle ? "进行中" : "未开始"}\n`;
            status += `战斗时长: ${battleStats.duration.toFixed(1)}s\n`;
            status += `参战者数量: ${battleStats.participantCount}\n`;
            
            status += `\nTimeline统计:\n`;
            status += `活跃: ${battleStats.timelineStats.activeCount}\n`;
            status += `暂停: ${battleStats.timelineStats.pausedCount}\n`;
            status += `总执行: ${battleStats.timelineStats.totalExecutedEvents}\n`;
        }
        
        this.statusLabel.string = status;
    }

    onDestroy() {
        if (this.battleManager) {
            this.battleManager.cleanup();
        }
    }
}
