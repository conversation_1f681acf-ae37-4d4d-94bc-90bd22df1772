import FightEvent from "../types/FightEvent";
import { ICharacter } from "../types/ICharacter";
import { ISkill, SkillType } from "../types/ISkill";
import { EventManager } from "./EventManager";

/**
 * 技能管理器
 * 负责管理角色的技能学习、使用和冷却
 */
export class SkillManager {
    private _character: ICharacter;
    private _skills: Map<string, ISkill> = new Map();
    private _basicAttackSkill: ISkill | null = null;
    private _eventManager: EventManager;
    constructor(character: ICharacter) {
        this._character = character;
        this._eventManager = new EventManager();
    }
    /**
     * 添加技能
     * @param skill 技能实例
     */
    addSkill(skill: ISkill): void {
        if (this._skills.has(skill.id)) {
            console.warn(`Skill ${skill.id} already exists, replacing...`);
        }
        this._skills.set(skill.id, skill);
        // 如果是基础攻击技能，设置为默认攻击技能
        if (skill.type === SkillType.BASIC_ATTACK) {
            this._basicAttackSkill = skill;
        }
        this._eventManager.emit(FightEvent.skillAdded, { skill, character: this._character });
    }
    /**
     * 移除技能
     * @param skillId 技能ID
     * @returns 是否移除成功
     */
    removeSkill(skillId: string): boolean {
        const skill = this._skills.get(skillId);
        if (!skill) {
            return false;
        }
        this._skills.delete(skillId);
        // 如果移除的是基础攻击技能，清空引用
        if (this._basicAttackSkill?.id === skillId) {
            this._basicAttackSkill = null;
        }
        this._eventManager.emit(FightEvent.skillRemoved, { skill, character: this._character });
        return true;
    }
    /**
     * 获取技能
     * @param skillId 技能ID
     * @returns 技能实例或null
     */
    getSkill(skillId: string): ISkill | null {
        return this._skills.get(skillId) || null;
    }
    /**
     * 获取基础攻击技能
     * @returns 基础攻击技能或null
     */
    getBasicAttackSkill(): ISkill | null {
        return this._basicAttackSkill;
    }
    /**
     * 设置基础攻击技能
     * @param skillId 技能ID
     */
    setBasicAttackSkill(skillId: string): void {
        const skill = this._skills.get(skillId);
        if (skill) {
            this._basicAttackSkill = skill;
        }
    }
    /**
     * 获取所有技能
     * @returns 技能数组
     */
    getAllSkills(): ISkill[] {
        return Array.from(this._skills.values());
    }
    /**
     * 根据类型获取技能
     * @param type 技能类型
     * @returns 技能数组
     */
    getSkillsByType(type: SkillType): ISkill[] {
        return this.getAllSkills().filter(skill => skill.type === type);
    }
    /**
     * 获取可用的技能
     * @returns 可用技能数组
     */
    getAvailableSkills(): ISkill[] {
        return this.getAllSkills().filter(skill => skill.canUse);
    }
    /**
     * 获取冷却中的技能
     * @returns 冷却中技能数组
     */
    getCooldownSkills(): ISkill[] {
        return this.getAllSkills().filter(skill => !skill.canUse && skill.remainingCooldown > 0);
    }
    /**
     * 检查是否拥有技能
     * @param skillId 技能ID
     * @returns 是否拥有
     */
    hasSkill(skillId: string): boolean {
        return this._skills.has(skillId);
    }
    /**
     * 检查技能是否可用
     * @param skillId 技能ID
     * @returns 是否可用
     */
    isSkillAvailable(skillId: string): boolean {
        const skill = this._skills.get(skillId);
        return skill ? skill.canUse : false;
    }
    /**
     * 获取技能冷却剩余时间
     * @param skillId 技能ID
     * @returns 剩余冷却时间（秒）
     */
    getSkillCooldownRemaining(skillId: string): number {
        const skill = this._skills.get(skillId);
        return skill ? skill.remainingCooldown : 0;
    }
    /**
     * 重置技能冷却
     * @param skillId 技能ID，如果不指定则重置所有技能
     */
    resetCooldown(skillId?: string): void {
        if (skillId) {
            const skill = this._skills.get(skillId);
            if (skill) {
                skill.resetCooldown();
                this._eventManager.emit(FightEvent.skillCooldownReset, { skill, character: this._character });
            }
        } else {
            for (const skill of this._skills.values()) {
                skill.resetCooldown();
            }
            this._eventManager.emit(FightEvent.allSkillsCooldownReset, { character: this._character });
        }
    }
    /**
     * 使用技能
     * @param skillId 技能ID
     * @param target 目标角色
     * @param position 目标位置
     * @returns 是否使用成功
     */
    useSkill(skillId: string, target?: ICharacter, position?: cc.Vec3): boolean {
        const skill = this._skills.get(skillId);
        if (!skill) {
            console.warn(`Skill ${skillId} not found`);
            return false;
        }
        if (!skill.canCastOn(this._character, target)) {
            console.warn(`Cannot cast skill ${skillId} on target`);
            return false;
        }
        const success = skill.cast(this._character, target, [], position);
        if (success) {
            this._eventManager.emit(FightEvent.skillUsed, { skill, character: this._character, target, position });
        }
        return success;
    }
    /**
     * 更新所有技能状态
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void {
        for (const skill of this._skills.values()) {
            skill.update(deltaTime);
        }
    }
    /**
     * 获取技能统计信息
     * @returns 统计信息
     */
    getSkillStats() {
        const stats = {
            totalSkills: this._skills.size,
            availableSkills: 0,
            cooldownSkills: 0,
            skillsByType: {} as any
        };
        for (const skill of this._skills.values()) {
            if (skill.canUse) {
                stats.availableSkills++;
            } else {
                stats.cooldownSkills++;
            }
            if (!stats.skillsByType[skill.type]) {
                stats.skillsByType[skill.type] = 0;
            }
            stats.skillsByType[skill.type]++;
        }
        return stats;
    }
    /**
     * 导出技能数据
     * @returns 技能数据
     */
    exportSkillData() {
        const data = {} as ISkill
        for (const [id, skill] of this._skills) {
            data[id] = {
                id: skill.id,
                name: skill.name,
                type: skill.type,
                level: skill.level,
                cooldown: skill.cooldown,
                remainingCooldown: skill.remainingCooldown,
                canUse: skill.canUse
            };
        }
        return data;
    }
    /**
     * 设置事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    on(event: string, callback: Function): void {
        this._eventManager.on(event, callback);
    }
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     */
    off(event: string, callback: Function): void {
        this._eventManager.off(event, callback);
    }
    /** * 清理技能管理器 */
    cleanup(): void {
        this._skills.clear();
        this._basicAttackSkill = null;
        this._eventManager.cleanup();
    }
    /** * 获取调试信息 */
    getDebugInfo() {
        return {
            characterId: this._character.id,
            skillCount: this._skills.size,
            basicAttackSkill: this._basicAttackSkill?.id || null,
            skills: this.exportSkillData(),
            stats: this.getSkillStats()
        };
    }
}
