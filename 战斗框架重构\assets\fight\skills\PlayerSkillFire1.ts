import { <PERSON>etLauncher } from "../systems/BulletSystem";
import { Timeline, TimelineNode } from "../timeline/Timeline";
import { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, FireBulletTimelineEvent, PlayEffectTimelineEvent } from "../timeline/TimelineEvents";
import { IBuff } from "../types/IBuff";
import { BulletType, TrajectoryType } from "../types/IBullet";
import { ICharacter } from "../types/ICharacter";
import { DamageType } from "../types/IDamage";
import { ISkill, SkillType, SkillTargetType } from "../types/ISkill";
import { ITimeline } from "../types/ITimeline";
import SkillName from "../types/SkillName";
/*** 火球术技能*/
export class PlayerSkillFire1 implements ISkill {
    private _id: string = SkillName.player_skill_fire1;
    private _name: string = "火球术";
    private _description: string = "发射一个火球，对敌人造成魔法伤害";
    private _cooldown: number = 2.0;
    private _remainingCooldown: number = 0;
    private _mpCost: number = 20;
    private _staminaCost: number = 0;
    private _level: number = 1;
    private _type: SkillType = SkillType.ACTIVE;
    private _targetType: SkillTargetType = SkillTargetType.SINGLE_ENEMY;
    private _range: number = 300;
    private _timeline: ITimeline | null = null;
    private _passiveBuffs: IBuff[] = [];
    /**技能配置 */
    private _config = {
        animationName: "skill_fire1",
        soundId: "fire_skill_cast",
        bulletPrefabPath: "prefabs/bullets/FireBall",
        hitEffectPath: "prefabs/effects/FireExplosion",
        hitSoundId: "fire_explosion",
        damage: 0, // 0表示使用施法者的攻击力
        damageType: DamageType.MAGIC
    };

    // 实现ISkill接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get cooldown(): number { return this._cooldown; }
    get remainingCooldown(): number { return this._remainingCooldown; }
    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }
    get mpCost(): number { return this._mpCost; }
    get staminaCost(): number { return this._staminaCost; }
    get level(): number { return this._level; }
    get type(): SkillType { return this._type; }
    get targetType(): SkillTargetType { return this._targetType; }
    get range(): number { return this._range; }
    get timeline(): ITimeline { return this._timeline!; }
    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }
    get canUse(): boolean { return this._remainingCooldown <= 0; }

    /** * 检查是否可以对目标使用技能 */
    canCastOn(caster: ICharacter, target?: ICharacter): boolean {
        if (!this.canUse) {
            return false;
        }
        if (!target || target.isDead) {
            return false;
        }
        // 检查目标是否为敌人
        if (target.role === caster.role) {
            return false;
        }
        const distance = cc.Vec3.distance(caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        return distance <= this._range;
    }
    /** * 释放技能 */
    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        // 这里应该将Timeline添加到TimelineManager
        // TimelineManager.getInstance().addTimeline(this._timeline);
        console.log(`${caster.name} casts ${this._name} on ${target?.name}`);
        return true;
    }
    /** * 更新技能状态 */
    update(deltaTime: number): void {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    }
    /** * 重置冷却时间 */
    resetCooldown(): void {
        this._remainingCooldown = 0;
    }
    /** * 创建技能Timeline */
    private createTimeline(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {
        const timeline = new Timeline(Timeline.getTimeLineId(this._id), `${this._name}_Timeline`, 3.0, caster, target, targets, position);
        // 0.0秒：播放施法动画
        const castAnimationNode = new TimelineNode("cast_animation", 0.0, new PlayAnimationTimelineEvent("cast_anim", this._config.animationName, false, true));
        timeline.addNode(castAnimationNode);
        // 0.1秒：播放施法音效
        const castSoundNode = new TimelineNode("cast_sound", 0.1, new PlaySoundTimelineEvent("cast_sound", this._config.soundId));
        timeline.addNode(castSoundNode);
        // 0.5秒：发射火球
        const fireBulletNode = new TimelineNode("fire_bullet", 0.5, new FireBulletTimelineEvent("fire_bullet", this.createBulletLauncher(caster), this._config.hitEffectPath, "hit", this._config.hitSoundId));
        timeline.addNode(fireBulletNode);
        // 1.0秒：播放命中特效（如果子弹命中）
        const hitEffectNode = new TimelineNode("hit_effect", 1.0, new PlayEffectTimelineEvent("hit_effect", this._config.hitEffectPath, true, cc.v3(0, 50, 0)));
        timeline.addNode(hitEffectNode);
        return timeline;
    }

    /** * 创建子弹发射器 */
    private createBulletLauncher(caster: ICharacter) {
        const bulletConfig = {
            id: "fire_ball",
            type: BulletType.STRAIGHT,
            prefabPath: this._config.bulletPrefabPath,
            speed: 400,
            lifeTime: 5.0,
            maxHits: 1,
            trajectory: { type: TrajectoryType.LINEAR },
            collision: { radius: 20, piercing: false, layers: ["enemy"], checkFrequency: 60 },
            visual: {
                trail: { enabled: true, length: 100, width: 10, color: cc.Color.ORANGE }
            },
            audio: {
                hitSound: this._config.hitSoundId
            }
        };
        // 创建发射器实例
        return new BulletLauncher("fire_ball_launcher", caster, bulletConfig);
    }

    /** * 检查资源消耗 */
    private checkResourceCost(caster: ICharacter): boolean {
        // 检查MP
        if (this._mpCost > 0) {
            const attributes = caster.attributes as any;
            if (attributes.hasEnoughMp && !attributes.hasEnoughMp(this._mpCost)) {
                console.log(`${caster.name} doesn't have enough MP (need ${this._mpCost})`);
                return false;
            }
        }
        // 检查耐力
        if (this._staminaCost > 0) {
            const attributes = caster.attributes as any;
            if (attributes.hasEnoughStamina && !attributes.hasEnoughStamina(this._staminaCost)) {
                console.log(`${caster.name} doesn't have enough stamina (need ${this._staminaCost})`);
                return false;
            }
        }
        return true;
    }

    /** * 消耗资源 */
    private consumeResources(caster: ICharacter): void {
        // 消耗MP
        if (this._mpCost > 0) {
            const attributes = caster.attributes as any;
            if (attributes.modifyMp) {
                attributes.modifyMp(-this._mpCost);
                console.log(`${caster.name} consumed ${this._mpCost} MP`);
            }
        }
        // 消耗耐力
        if (this._staminaCost > 0) {
            const attributes = caster.attributes as any;
            if (attributes.modifyStamina) {
                attributes.modifyStamina(-this._staminaCost);
                console.log(`${caster.name} consumed ${this._staminaCost} stamina`);
            }
        }
    }

    /** * 升级技能 */
    levelUp(): void {
        this._level++;
        // 根据等级调整技能属性
        this._mpCost = Math.max(10, this._mpCost - 1); // MP消耗减少
        this._cooldown = Math.max(1.0, this._cooldown - 0.1); // 冷却时间减少
        this._range += 20; // 射程增加
        console.log(`${this._name} leveled up to ${this._level}`);
    }
    /** * 获取技能信息 */
    getSkillInfo() {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType
        };
    }

    /** * 技能被添加时的回调（用于多目标技能等特殊情况） */
    onAdd?(targets: ICharacter[]): void {
        // 火球术是单目标技能，不需要特殊处理
        console.log(`${this._name} added with ${targets.length} targets`);
    }
}
