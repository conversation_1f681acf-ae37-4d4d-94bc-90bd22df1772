# 战斗系统测试场景说明

## 📋 概述

`BattleTestScene.ts` 是一个综合的战斗系统测试场景，整合了原来的三个测试案例：
- **BattleTestScene** - 基础战斗测试
- **UsageExample** - 完整使用示例  
- **TimelineTest** - Timeline系统测试

## 🎮 功能特性

### 基础战斗功能
- ✅ 角色创建和管理
- ✅ 技能释放测试
- ✅ Buff添加测试
- ✅ 战斗开始/结束控制
- ✅ 实时状态显示

### Timeline系统测试
- ✅ 基础Timeline创建
- ✅ 伤害Timeline测试
- ✅ Buff Timeline测试
- ✅ Timeline事件执行

### AI和多敌人系统
- ✅ 多敌人创建（最多3个）
- ✅ AI自动战斗模式
- ✅ 智能目标选择
- ✅ 自动技能释放

### 调试和监控
- ✅ 详细的调试信息输出
- ✅ 实时战斗统计
- ✅ Timeline和子弹管理统计
- ✅ 角色状态监控

## 🎯 UI按钮说明

### 基础战斗按钮
| 按钮 | 功能 | 说明 |
|------|------|------|
| **开始战斗** | 启动战斗 | 根据当前模式开始单敌人或多敌人战斗 |
| **结束战斗** | 结束战斗 | 手动结束当前战斗，重置所有状态 |
| **释放技能** | 技能测试 | 玩家对敌人释放火球术技能 |
| **添加Buff** | Buff测试 | 给玩家添加反击Buff |

### Timeline测试按钮
| 按钮 | 功能 | 说明 |
|------|------|------|
| **测试Timeline** | 基础Timeline | 创建包含音效事件的简单Timeline |
| **测试伤害Timeline** | 伤害Timeline | 创建造成80点物理伤害的Timeline |
| **测试Buff Timeline** | Buff Timeline | 创建给敌人添加反击Buff的Timeline |

### AI和多敌人按钮
| 按钮 | 功能 | 说明 |
|------|------|------|
| **创建多敌人** | 多敌人模式 | 创建3个不同强度的敌人 |
| **开启/关闭AI** | AI控制 | 启用自动战斗AI，玩家和敌人自动战斗 |
| **调试信息** | 调试输出 | 在控制台输出详细的系统状态信息 |

## 🚀 使用流程

### 基础测试流程
1. **启动场景** → 自动创建玩家和敌人
2. **开始战斗** → 激活战斗系统
3. **释放技能** → 测试技能和Timeline系统
4. **添加Buff** → 测试Buff系统
5. **结束战斗** → 清理和重置

### Timeline专项测试
1. **开始战斗** → 确保战斗系统激活
2. **测试Timeline** → 验证基础Timeline功能
3. **测试伤害Timeline** → 验证伤害计算和应用
4. **测试Buff Timeline** → 验证Buff添加机制
5. **观察控制台** → 查看Timeline执行日志

### AI和多敌人测试
1. **创建多敌人** → 切换到多敌人模式
2. **开始战斗** → 启动多敌人战斗
3. **开启AI** → 激活自动战斗
4. **观察战斗** → 查看AI行为和战斗过程
5. **调试信息** → 查看详细系统状态

## 📊 状态面板说明

状态面板实时显示以下信息：

### 角色信息
- **玩家状态**: HP/MP、存活状态、技能数、Buff数
- **敌人状态**: 单敌人或多敌人的HP和存活状态

### 系统状态
- **测试模式**: basic（基础）/ multi（多敌人）
- **AI状态**: 启用/禁用
- **战斗状态**: 进行中/未开始、时长、参战者数量

### 统计信息
- **Timeline统计**: 活跃数量、暂停数量、总执行事件数
- **子弹统计**: 活跃数量、总创建数、总销毁数

## 🔧 调试功能

### 控制台日志
- **战斗事件**: 技能释放、伤害计算、Buff添加/移除
- **Timeline事件**: Timeline创建、事件执行、完成状态
- **AI行为**: 自动攻击、技能释放、目标选择
- **系统状态**: 管理器状态、统计信息

### 调试信息按钮
点击"调试信息"按钮可以在控制台输出：
- BattleManager详细状态
- TimelineManager详细状态  
- 所有角色的完整信息
- 当前测试模式和AI状态

## 🎯 测试建议

### 基础功能验证
1. 验证角色创建和属性设置
2. 验证技能释放和Timeline创建
3. 验证Buff添加和效果
4. 验证战斗开始/结束流程

### Timeline系统验证
1. 验证Timeline事件按时间执行
2. 验证伤害计算和应用正确
3. 验证Buff通过Timeline正确添加
4. 验证Timeline统计信息准确

### AI系统验证
1. 验证多敌人创建和管理
2. 验证AI自动选择最近目标
3. 验证AI技能释放优先级
4. 验证战斗结束条件判断

### 性能和稳定性
1. 长时间运行AI战斗
2. 频繁创建/销毁Timeline
3. 大量子弹和Buff管理
4. 内存泄漏检测

## 📝 注意事项

1. **按钮顺序**: 建议先开始战斗再进行其他测试
2. **AI模式**: AI启用时会自动战斗，可能很快结束战斗
3. **多敌人模式**: 创建多敌人后需要重新开始战斗
4. **调试信息**: 大量日志可能影响性能，适量使用
5. **Timeline测试**: 可以同时创建多个Timeline观察并发执行

## 🔄 与原测试案例的对比

| 功能 | 原BattleTestScene | 原UsageExample | 原TimelineTest | 新综合场景 |
|------|------------------|----------------|----------------|------------|
| 基础战斗 | ✅ | ✅ | ❌ | ✅ |
| Timeline测试 | ❌ | ❌ | ✅ | ✅ |
| 多敌人 | ❌ | ✅ | ❌ | ✅ |
| AI系统 | ❌ | ✅ | ❌ | ✅ |
| 事件系统 | ❌ | ✅ | ❌ | ✅ |
| 调试功能 | 基础 | 基础 | 基础 | 完整 |

现在你只需要使用一个测试场景就能测试所有功能！
