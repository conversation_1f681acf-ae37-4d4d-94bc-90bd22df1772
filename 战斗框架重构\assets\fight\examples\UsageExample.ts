import { BuffModelBeHurtFight } from "../actions/BuffModelBeHurtFight";
import { Character } from "../characters/Character";
import { PlayerSkillFire1 } from "../skills/PlayerSkillFire1";
import { BattleManager } from "../systems/BattleManager";
import { CharacterCreateInfo, CharacterRole } from "../types/CharacterTypes";
import { ICharacterEvents } from "../types/ICharacter";


const { ccclass, property } = cc._decorator;

/*** 使用示例类*/
@ccclass
export class UsageExample extends cc.Component {
    @property(cc.Prefab)
    public playerPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    public enemyPrefab: cc.Prefab = null;
    @property(cc.Node)
    public battleField: cc.Node = null;

    private player: Character = null;
    private enemies: Character[] = [];
    /** * 手动测试功能 */
    @property(cc.Button)
    public testButton: cc.Button = null;

    protected onLoad(): void {
        if (this.testButton) {
            this.testButton.node.on('click', this.runTests, this);
        }
    }

    start() {
        this.createPlayer();
        this.createEnemies();
        this.setupBattle();
    }
    /** * 创建玩家角色 */
    private createPlayer(): void {
        // 创建角色节点
        const playerNode = cc.instantiate(this.playerPrefab);
        playerNode.parent = this.battleField;
        playerNode.position = cc.v3(-200, 0, 0);
        // 获取角色组件
        this.player = playerNode.getComponent(Character);
        // 设置角色数据
        const playerData: CharacterCreateInfo = {
            prefabKey: "player",
            role: CharacterRole.HERO,
            name: "勇者",
            worldPosition: cc.v3(-200, 0, 0),
            parent: this.battleField,
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                attack: 100,
                defense: 50,
                attackSpeed: 1.2,
                moveSpeed: 150,
                attackRange: 200,
                criticalRate: 0.1,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.05,
                level: 10,
                experience: 0
            }
        };
        this.player.setCharacterData(playerData);
        // 设置事件监听
        this.setupPlayerEvents();
        // 学习技能
        this.teachPlayerSkills();
        console.log("玩家角色创建完成:", this.player.getCharacterInfo());
    }
    /** * 创建敌人 */
    private createEnemies(): void {
        for (let i = 0; i < 3; i++) {
            const enemyNode = cc.instantiate(this.enemyPrefab);
            enemyNode.parent = this.battleField;
            enemyNode.position = cc.v3(200 + i * 100, 0, 0);
            const enemy = enemyNode.getComponent(Character);
            const enemyData: CharacterCreateInfo = {
                prefabKey: "enemy",
                role: CharacterRole.ENEMY,
                name: `敌人${i + 1}`,
                worldPosition: cc.v3(200 + i * 100, 0, 0),
                parent: this.battleField,
                initialAttributes: {
                    hp: 500,
                    maxHp: 500,
                    mp: 100,
                    maxMp: 100,
                    attack: 80,
                    defense: 30,
                    attackSpeed: 1.0,
                    moveSpeed: 100,
                    attackRange: 150,
                    criticalRate: 0.05,
                    criticalDamage: 1.2,
                    hitRate: 0.9,
                    dodgeRate: 0.1,
                    level: 5,
                    experience: 0
                }
            };
            enemy.setCharacterData(enemyData);
            this.setupEnemyEvents(enemy);
            this.enemies.push(enemy);
            console.log(`敌人${i + 1}创建完成:`, enemy.getCharacterInfo());
        }
    }

    /** * 设置玩家事件 */
    private setupPlayerEvents(): void {
        const events: ICharacterEvents = {
            onDeath: (character) => {
                console.log(`${character.name} 死亡了！`);
                this.onPlayerDeath();
            },
            onTakeDamage: (character, damage, attacker) => {
                console.log(`${character.name} 受到了 ${damage} 点伤害`);
                if (attacker) {
                    console.log(`攻击者: ${attacker.name}`);
                }
            },
            onHeal: (character, healAmount) => {
                console.log(`${character.name} 恢复了 ${healAmount} 点生命值`);
            },
            onSkillCast: (character, skillName) => {
                console.log(`${character.name} 释放了技能: ${skillName}`);
            },
            onBuffAdded: (character, buff) => {
                console.log(`${character.name} 获得了Buff: ${buff.name}`);
            },
            onBuffRemoved: (character, buffId) => {
                console.log(`${character.name} 失去了Buff: ${buffId}`);
            }
        };
        this.player.setEvents(events);
    }
    /*** 设置敌人事件*/
    private setupEnemyEvents(enemy: Character): void {
        const events: ICharacterEvents = {
            onDeath: (character) => {
                console.log(`${character.name} 被击败了！`);
                this.onEnemyDeath(character as Character);
            },
            onTakeDamage: (character, damage, _attacker) => {
                console.log(`${character.name} 受到了 ${damage} 点伤害`);
            }
        };
        enemy.setEvents(events);
    }
    /** * 教授玩家技能 */
    private teachPlayerSkills(): void {
        // 创建火球术技能
        const fireSkill = new PlayerSkillFire1();
        this.player.learnSkill(fireSkill);
        console.log(`玩家学会了技能: ${fireSkill.name}`);
    }
    /*** 添加Buff示例*/
    private addBuffsToPlayer(): void {
        // 创建受伤反击Buff
        const counterBuff = new BuffModelBeHurtFight(this.player, this.player);
        this.player.addBuff(counterBuff);
        console.log(`玩家获得了Buff: ${counterBuff.name}`);
        console.log(`Buff效果: 受到伤害时有${counterBuff.getEffectValue('counterAttackChance') * 100}%概率反击`);
    }
    /** * 设置战斗 */
    private setupBattle(): void {
        // 添加Buff到玩家
        this.addBuffsToPlayer();

        // 初始化战斗管理器
        const battleManager = BattleManager.getInstance();
        const participants = [this.player, ...this.enemies];
        battleManager.startBattle("usage_example_battle", participants);

        // 设置简单的AI行为
        this.schedule(() => {
            this.updateBattleAI();
            // 更新战斗管理器（包含Timeline管理器）
            battleManager.update(1.0);
        }, 1.0);
        console.log("战斗开始！");
        console.log("战斗管理器已初始化");
    }
    /*** 更新战斗AI*/
    private updateBattleAI(): void {
        if (!this.player || this.player.isDead) {
            return;
        }
        // 玩家攻击最近的敌人
        const nearestEnemy = this.findNearestEnemy();
        if (nearestEnemy) {
            // 尝试释放技能
            if (this.player.castSkill("player_skill_fire1", nearestEnemy.node)) {
                console.log(`${this.player.name} cast fire skill on ${nearestEnemy.name}`);
            } else if (this.player.isInAttackRange && this.player.isInAttackRange(nearestEnemy)) {
                // 如果技能释放失败，使用普通攻击
                this.player.attack(nearestEnemy);
            }
        }
        // 敌人攻击玩家
        for (const enemy of this.enemies) {
            if (!enemy.isDead && enemy.isInAttackRange(this.player)) {
                enemy.attack(this.player);
            }
        }
    }
    /** * 寻找最近的敌人 */
    private findNearestEnemy(): Character | null {
        let nearestEnemy: Character | null = null;
        let minDistance = Infinity;
        for (const enemy of this.enemies) {
            if (enemy.isDead) continue;
            const distance = cc.Vec3.distance(this.player.node.position, enemy.node.position);
            if (distance < minDistance) {
                minDistance = distance;
                nearestEnemy = enemy;
            }
        }
        return nearestEnemy;
    }
    /** * 玩家死亡处理 */
    private onPlayerDeath(): void {
        console.log("游戏结束 - 玩家死亡");
        this.unscheduleAllCallbacks();
    }
    /** * 敌人死亡处理 */
    private onEnemyDeath(enemy: Character): void {
        const index = this.enemies.indexOf(enemy);
        if (index >= 0) {
            this.enemies.splice(index, 1);
        }
        if (this.enemies.length === 0) {
            console.log("胜利 - 所有敌人被击败");
            this.unscheduleAllCallbacks();
        }
    }


    /** * 运行测试 */
    private runTests(): void {
        console.log("=== 开始测试 ===");
        if (this.player) {
            // 测试移动
            console.log("测试移动...");
            this.player.move(cc.v3(50, 0, 0));
            // 测试技能释放
            console.log("测试技能释放...");
            this.player.castSkill("heal");
            // 测试属性查看
            console.log("玩家信息:", this.player.getCharacterInfo());
        }
        console.log("=== 测试完成 ===");
    }
}
